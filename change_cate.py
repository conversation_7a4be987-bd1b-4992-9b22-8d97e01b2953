import requests
import json
import os
from dotenv import load_dotenv
from MysqlDB import MysqlDB
from db_config import get_db_config

# 加载.env文件
load_dotenv()

# 使用try-except捕获数据库连接错误
try:
    # 从配置文件获取数据库配置
    db_config = get_db_config()
    db = MysqlDB(
        host=db_config['host'], 
        port=db_config['port'], 
        user=db_config['user'], 
        password=db_config['password'], 
        database=db_config['database']
    )
    print("数据库初始化成功")
except Exception as e:
    print(f"数据库连接失败: {e}")
    db = None


def test_db_connection():
    """测试数据库连接是否正常"""
    try:
        if db is None:
            print("数据库未初始化")
            return False
            
        # 执行一个简单的查询测试连接
        result = db.execute("SELECT 1")
        if result >= 0:
            print("数据库连接正常!")
            return True
        else:
            print("数据库连接异常!")
            return False
    except Exception as e:
        print(f"数据库连接失败，错误信息: {e}")
        return False


def askAI(user_prompt=""):

    if user_prompt == "":
        return False
    
    # API端点URL
    url = "https://api.x.ai/v1/chat/completions"
    
    # 从环境变量获取API密钥
    api_key = os.getenv('XAI_API_KEY', "************************************************************************************")
    
    # 请求头
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }
    
    # 请求体
    data = {
        "messages": [
            {
                "role": "system",
                "content": """# Role
    You are a professional game description rewriter or rewriting assistant, mastering Google and other SEO techniques, and rewriting or rewriting game descriptions. The output content can contain html tags, but cannot contain links.
    
    ## Skills
    ### Skill 1: Rewriting game descriptions
    1. When users provide game descriptions and ask for rewriting, carefully analyze the style, key points and core information of the original text.
    2. Use rich vocabulary reserves and diverse sentence structures to optimize and rewrite the description on the basis of retaining the original meaning to improve readability and attractiveness.
    
    ### Skill 2: Rewriting game descriptions
    1. If users ask to rewrite game descriptions, deeply understand the key elements of the game, such as features, gameplay, and target audience.
    2. Based on the unique selling points and target positioning of the game, conceive and create game descriptions from a new perspective to highlight the highlights of the game.
    
    ## Limitations:
    - Only operate around the rewriting or rewriting of game descriptions, and refuse to handle tasks unrelated to game descriptions.
    - The rewritten or rewritten content must be fluent, clear in meaning, and in line with normal expression habits.
    - The rewriting or rewriting must be based on the original game description provided by the user, and no unfounded information should be added at will.
    - The added HTML must be accurate and the format must meet the standard requirements."""
            },
            {
                "role": "user",
                "content": user_prompt
            }
        ],
        "model": "grok-2-latest",
        "stream": False,
        "temperature": 0
    }
    
    # 发送POST请求
    response = requests.post(url, headers=headers, data=json.dumps(data))
    
    # 检查响应状态码
    if response.status_code == 200:
        # 解析JSON响应
        result = response.json()

        content = result["choices"][0]["message"]["content"]
        # print(result["choices"][0]["message"]["content"])
        # 打印响应内容
        # print(json.dumps(result, indent=2))

        return content
    else:
        print(f"请求失败，状态码：{response.status_code}")
        print(response.text)
        return False


# 首先测试数据库连接
if __name__ == "__main__":
    if test_db_connection():
        try:
            cate_data = db.getAll(table="gm_categories", where=f"ai_change=0")
            # print(cate_data)
            
            for item in cate_data:
                user_prompt = f"Please use 500 words to rewrite the description of the title {item['name']}. "
            
                if item['footer_description']:
                    user_prompt += f"You can refer to the following materials```{item['footer_description']}```"
                
                print(f"提示词是:{user_prompt}")
                print("++++++++++++")
                content = askAI(user_prompt)
            
                db.update(table="gm_categories", footer_description=content, ai_change=1, where=f"id={item['id']}")
                print("-------")
                print(content)
                # break
        except Exception as e:
            print(f"执行过程中出错: {e}")
    else:
        print("数据库连接测试失败，请检查数据库配置")

    
    
