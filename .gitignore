# Python编译文件
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# 环境配置
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
.python-version

# IDE和编辑器文件
.idea/
.vscode/
*.swp
*.swo
*~
.DS_Store

# 日志文件
*.log
logs/
log/

# 本地配置文件
config.local.py
local_settings.py

# 测试文件和临时文件
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
*.tmp
*.bak
*.backup
temp/
tmp/

# 数据库文件
*.db
*.sqlite3
*.sql
*.dump

# 其他不需要版本控制的文件
.history/
.ipynb_checkpoints/ 