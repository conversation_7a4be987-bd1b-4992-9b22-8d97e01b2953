import requests
import json
import time
import os
from dotenv import load_dotenv
from MysqlDB import MysqlDB
from db_config import get_db_config

# 加载.env文件
load_dotenv()

# 使用try-except捕获数据库连接错误
try:
    # 从配置文件获取数据库配置
    db_config = get_db_config()
    db = MysqlDB(
        host=db_config['host'], 
        port=db_config['port'], 
        user=db_config['user'], 
        password=db_config['password'], 
        database=db_config['database']
    )
    print("数据库初始化成功")
except Exception as e:
    print(f"数据库连接失败: {e}")
    db = None


def test_db_connection():
    """测试数据库连接是否正常"""
    try:
        if db is None:
            print("数据库未初始化")
            return False
            
        # 执行一个简单的查询测试连接
        result = db.execute("SELECT 1")
        if result >= 0:
            print("数据库连接正常!")
            return True
        else:
            print("数据库连接异常!")
            return False
    except Exception as e:
        print(f"数据库连接失败，错误信息: {e}")
        return False


def askAI(user_prompt=""):
    """使用xAI API获取AI回复"""
    if user_prompt == "":
        return False
    
    # API端点URL
    url = "https://api.x.ai/v1/chat/completions"
    
    # 从环境变量获取API密钥
    api_key = os.getenv('XAI_API_KEY', "************************************************************************************")
    
    # 请求头
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }
    
    # 请求体
    data = {
        "messages": [
            {
                "role": "system",
                "content": """# 角色
您是一位英文的专业的游戏内容优化专家，精通SEO技术和游戏行业知识。您的任务是优化英文游戏标题和描述，使其更具吸引力并符合搜索引擎优化原则。

## 技能
### 技能1: 游戏标题优化
1. 当用户提供游戏标题和描述时，分析其核心玩法、类型和特色。
2. 创建简洁、有吸引力且富有描述性的标题，突出游戏的独特卖点。
3. 确保标题包含相关关键词，但避免关键词堆砌。
4. 标题应当简短精炼，通常不超过8个单词。

### 技能2: 游戏描述优化
1. 编写生动、吸引人且信息丰富的游戏描述，详细介绍游戏玩法、特色和体验。
2. 使用SEO友好的结构，包括小标题、简短段落和项目符号列表。
3. 在描述中自然融入相关关键词，优化搜索引擎表现。
4. 结尾应当包含明确的号召性用语，鼓励用户尝试游戏。
5. 描述内容可以含有HTML标签以增强可读性，但不包含链接。

## 限制:
- 仅针对游戏标题和描述进行优化，拒绝处理与此无关的任务。
- 优化后的内容必须流畅自然，避免机械或不自然的表达。
- 不添加虚假或误导性信息，所有内容须基于原始游戏信息。
- 添加的HTML标签须符合标准格式要求，不包含JavaScript或链接元素。"""
            },
            {
                "role": "user",
                "content": user_prompt
            }
        ],
        "model": "grok-2-latest",
        "stream": False,
        "temperature": 0.7
    }
    
    # 发送POST请求
    try:
        response = requests.post(url, headers=headers, data=json.dumps(data))
        
        # 检查响应状态码
        if response.status_code == 200:
            # 解析JSON响应
            result = response.json()
            content = result["choices"][0]["message"]["content"]
            return content
        else:
            print(f"请求失败，状态码：{response.status_code}")
            print(response.text)
            return False
    except Exception as e:
        print(f"API请求异常: {e}")
        return False


def optimize_game_title_and_description(batch_size=5, delay=3):
    """优化游戏标题和描述"""
    if not test_db_connection():
        print("数据库连接测试失败，无法继续")
        return False
    
    try:
        # 获取需要优化的游戏数据
        games_data = db.getAll(
            table="gm_games", 
            where="ai_change=0", 
            order="game_id ASC", 
            limit=f"0,{batch_size}"
        )
        
        if not games_data or len(games_data) == 0:
            print("没有找到需要优化的游戏数据")
            return False
        
        print(f"找到 {len(games_data)} 个需要优化的游戏")
        
        for game in games_data:
            game_id = game['game_id']
            name = game['name']  # 正确的游戏标题字段是name
            description = game['description']
            
            print(f"正在优化游戏 ID: {game_id}, 名称: {name}")
            
            # 构建提示词
            user_prompt = f"""请优化以下游戏的标题和描述，使其更吸引人并符合SEO优化规则:

游戏当前标题: {name}

游戏当前描述:
{description}

请按以下格式返回结果:
新标题: [优化后的标题]

新描述:
[优化后的描述]
"""
            
            # 获取AI回复
            ai_response = askAI(user_prompt)
            
            if not ai_response:
                print(f"游戏 {game_id} 优化失败，跳过")
                continue
            
            # 解析AI回复
            try:
                # 提取新标题
                if "新标题:" in ai_response:
                    new_title_parts = ai_response.split("新标题:")
                    new_title = new_title_parts[1].split("\n\n")[0].strip()
                    
                    # 如果标题后有换行，只取第一行
                    if "\n" in new_title:
                        new_title = new_title.split("\n")[0].strip()
                else:
                    new_title = name  # 保持原标题
                
                # 提取新描述
                if "新描述:" in ai_response:
                    new_description_parts = ai_response.split("新描述:")
                    new_description = new_description_parts[1].strip()
                else:
                    new_description = ai_response  # 使用整个回复作为描述
                
                # 更新数据库
                update_result = db.update(
                    table="gm_games", 
                    name=new_title,  # 更新name字段而不是game_name
                    description=new_description, 
                    ai_change=1, 
                    published='1',
                    where=f"game_id={game_id}"
                )
                
                print(f"游戏 {game_id} 更新结果: {'成功' if update_result > 0 else '未更改'}")
                print(f"新标题: {new_title}")
                print("新描述前200字符:", new_description[:200] + "...")
                print("-" * 50)
                
            except Exception as e:
                print(f"处理游戏 {game_id} 的AI回复时出错: {e}")
            
            # 添加延迟避免API限制
            if delay > 0:
                time.sleep(delay)
    
    except Exception as e:
        print(f"优化过程中出错: {e}")
        return False
    
    return True


# 主程序入口
if __name__ == "__main__":
    if test_db_connection():
        print("开始优化游戏标题和描述...")
        optimize_game_title_and_description(batch_size=1000, delay=5)
        print("优化过程已完成!")
    else:
        print("数据库连接测试失败，请检查数据库配置") 